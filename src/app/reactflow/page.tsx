'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { FlowCanvas } from '@/components/reactflow/flow-canvas'
import { Share2, Users } from 'lucide-react'

export default function ReactFlowPage() {
  return (
    <div className="container mx-auto max-w-7xl px-4 py-8 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header with Collaborative Link */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">React Flow</h1>
            <p className="text-muted-foreground">Interactive node-based editor</p>
          </div>
          <Link href="/reactflow/collaborative">
            <Button className="gap-2">
              <Share2 className="h-4 w-4" />
              Try Collaborative Flow
            </Button>
          </Link>
        </div>

        {/* Flow Canvas */}
        <Card className="h-[600px] overflow-hidden">
          <CardContent className="h-full p-0">
            <FlowCanvas />
          </CardContent>
        </Card>

        {/* Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Want to collaborate in real-time?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Try our collaborative React Flow feature with real-time synchronization,
              live cursors, and multi-user editing capabilities.
            </p>
            <Link href="/reactflow/collaborative">
              <Button variant="outline" className="gap-2">
                <Share2 className="h-4 w-4" />
                Open Collaborative Flow
              </Button>
            </Link>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
